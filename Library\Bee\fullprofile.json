{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 888, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 888, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 888, "tid": 22, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 888, "tid": 22, "ts": 1750344464328915, "dur": 838, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464334617, "dur": 1131, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 888, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464077056, "dur": 14928, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464091985, "dur": 224232, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464091998, "dur": 29, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464092030, "dur": 44175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464136220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464136224, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464136276, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464136282, "dur": 1474, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137769, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137773, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137850, "dur": 9, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137862, "dur": 58, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137924, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137927, "dur": 45, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464137975, "dur": 49, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138027, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138028, "dur": 56, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138089, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138091, "dur": 57, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138152, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138155, "dur": 51, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138210, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138213, "dur": 105, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138324, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138386, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138389, "dur": 44, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138437, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138440, "dur": 52, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138509, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138512, "dur": 47, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138563, "dur": 5, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138570, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138621, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138624, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138657, "dur": 37, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138698, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138700, "dur": 54, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138756, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138758, "dur": 26, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138787, "dur": 36, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138826, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138827, "dur": 60, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138893, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138944, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138945, "dur": 41, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138990, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464138993, "dur": 44, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139039, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139042, "dur": 35, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139079, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139081, "dur": 42, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139125, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139127, "dur": 56, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139185, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139186, "dur": 37, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139226, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139229, "dur": 38, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139269, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139271, "dur": 46, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139320, "dur": 34, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139357, "dur": 35, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139394, "dur": 34, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139431, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139434, "dur": 36, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139472, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139474, "dur": 41, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139520, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139523, "dur": 29, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139556, "dur": 30, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139589, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139591, "dur": 46, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139640, "dur": 1, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139643, "dur": 48, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139694, "dur": 1, "ph": "X", "name": "ProcessMessages 1009", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139696, "dur": 40, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139739, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139741, "dur": 37, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139780, "dur": 1, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139782, "dur": 41, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139825, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139827, "dur": 45, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139875, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139877, "dur": 50, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139929, "dur": 1, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139932, "dur": 45, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139979, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464139981, "dur": 41, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140024, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140026, "dur": 42, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140071, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140073, "dur": 35, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140111, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140112, "dur": 59, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140173, "dur": 1, "ph": "X", "name": "ProcessMessages 1457", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140176, "dur": 42, "ph": "X", "name": "ReadAsync 1457", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140221, "dur": 1, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140223, "dur": 40, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140266, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140268, "dur": 40, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140311, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140313, "dur": 39, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140354, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140356, "dur": 42, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140401, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140403, "dur": 37, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140442, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140444, "dur": 37, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140485, "dur": 38, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140526, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140528, "dur": 44, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140575, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140577, "dur": 35, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140615, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140617, "dur": 36, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140656, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140658, "dur": 42, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140702, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140704, "dur": 37, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140743, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140745, "dur": 38, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140786, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140788, "dur": 40, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140831, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140833, "dur": 37, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140872, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140874, "dur": 48, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140924, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140926, "dur": 32, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140960, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140961, "dur": 31, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464140995, "dur": 26, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141024, "dur": 31, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141059, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141122, "dur": 1, "ph": "X", "name": "ProcessMessages 1131", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141125, "dur": 45, "ph": "X", "name": "ReadAsync 1131", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141172, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141174, "dur": 38, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141216, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141218, "dur": 41, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141262, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141264, "dur": 40, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141307, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141309, "dur": 45, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141355, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141357, "dur": 35, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141395, "dur": 31, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141428, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141430, "dur": 69, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141502, "dur": 39, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141543, "dur": 1, "ph": "X", "name": "ProcessMessages 1764", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141545, "dur": 38, "ph": "X", "name": "ReadAsync 1764", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141584, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141633, "dur": 56, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141692, "dur": 2, "ph": "X", "name": "ProcessMessages 1751", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141695, "dur": 48, "ph": "X", "name": "ReadAsync 1751", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141745, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141748, "dur": 42, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141792, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141794, "dur": 43, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141839, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141841, "dur": 40, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141884, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141886, "dur": 56, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141944, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464141947, "dur": 79, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142030, "dur": 1, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142032, "dur": 120, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142155, "dur": 3, "ph": "X", "name": "ProcessMessages 3015", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142159, "dur": 42, "ph": "X", "name": "ReadAsync 3015", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142204, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142206, "dur": 75, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142283, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142284, "dur": 35, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142322, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142323, "dur": 36, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142361, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142363, "dur": 58, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142423, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142425, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142449, "dur": 19, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142470, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142516, "dur": 34, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142553, "dur": 44, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142598, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142600, "dur": 31, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142634, "dur": 33, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142668, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142670, "dur": 36, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142708, "dur": 28, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142740, "dur": 63, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142813, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142875, "dur": 1, "ph": "X", "name": "ProcessMessages 1615", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142878, "dur": 38, "ph": "X", "name": "ReadAsync 1615", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142919, "dur": 28, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142950, "dur": 34, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142986, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464142988, "dur": 50, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143039, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143040, "dur": 44, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143087, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143089, "dur": 48, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143140, "dur": 32, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143187, "dur": 38, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143227, "dur": 1, "ph": "X", "name": "ProcessMessages 1229", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143238, "dur": 32, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143272, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143273, "dur": 29, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143304, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143305, "dur": 27, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143336, "dur": 31, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143368, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143370, "dur": 29, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143401, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143402, "dur": 64, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143468, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143470, "dur": 20, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143492, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143515, "dur": 40, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143558, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143592, "dur": 31, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143625, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143626, "dur": 144, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143773, "dur": 3, "ph": "X", "name": "ProcessMessages 2669", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143777, "dur": 198, "ph": "X", "name": "ReadAsync 2669", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464143981, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144122, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144125, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144161, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144164, "dur": 102, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144270, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144293, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144295, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144317, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144319, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144331, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144338, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144358, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144366, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144374, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144391, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144399, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144406, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144412, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144473, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144495, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144503, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144511, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144548, "dur": 45, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144594, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144605, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144607, "dur": 5, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144614, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144806, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144866, "dur": 3, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464144870, "dur": 386, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145262, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145295, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145299, "dur": 7, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145350, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145399, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145401, "dur": 5, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145408, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145449, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145466, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145468, "dur": 15, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145485, "dur": 424, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145913, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145915, "dur": 59, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145977, "dur": 4, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464145982, "dur": 36, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146022, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146024, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146057, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146059, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146096, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146098, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146181, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146183, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146195, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146202, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146247, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146294, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146296, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146345, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146373, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146375, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146392, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146431, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146433, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146456, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146501, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146622, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146625, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146639, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146703, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146755, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146757, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146825, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146858, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146860, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146929, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146931, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146964, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146992, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464146994, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464147010, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464147013, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464147052, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464147055, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464147116, "dur": 4659, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464151785, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464151790, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464151849, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464151887, "dur": 397, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464152320, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464152333, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464152337, "dur": 841, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464153182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464153184, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464153285, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464153288, "dur": 13, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464153304, "dur": 861, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464154172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464154174, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464154206, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464154208, "dur": 895, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155110, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155113, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155137, "dur": 10, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155154, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155243, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155257, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155296, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155321, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155533, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155552, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155626, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155648, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155651, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155704, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155765, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155854, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155893, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155896, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155969, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464155995, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156525, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156574, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156577, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156640, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156642, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156784, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156817, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156819, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464156861, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157010, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157063, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157260, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157284, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157287, "dur": 118, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157408, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157471, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157473, "dur": 141, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157624, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157648, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157650, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157699, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157728, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157837, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464157859, "dur": 621, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158486, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158519, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158521, "dur": 170, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158697, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464158729, "dur": 538, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159273, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159315, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159317, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159355, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159382, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159511, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464159559, "dur": 137356, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464296926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464296929, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464296970, "dur": 87, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464297060, "dur": 1073, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464298143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464298216, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464298281, "dur": 21, "ph": "X", "name": "ProcessMessages 2976", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464298304, "dur": 1816, "ph": "X", "name": "ReadAsync 2976", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464300126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464300129, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464300164, "dur": 21, "ph": "X", "name": "ProcessMessages 1503", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464300187, "dur": 10113, "ph": "X", "name": "ReadAsync 1503", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464310306, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464310310, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464310355, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 888, "tid": 21474836480, "ts": 1750344464310357, "dur": 5849, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464335753, "dur": 771, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 888, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 888, "tid": 17179869184, "ts": 1750344464073797, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 888, "tid": 17179869184, "ts": 1750344464073805, "dur": 18174, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 888, "tid": 17179869184, "ts": 1750344464091980, "dur": 40, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464336526, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 888, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 888, "tid": 1, "ts": 1750344460883208, "dur": 6475, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 888, "tid": 1, "ts": 1750344460889688, "dur": 37259, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 888, "tid": 1, "ts": 1750344460926958, "dur": 30668, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464336536, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 888, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460881375, "dur": 12503, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460893880, "dur": 2775764, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460894883, "dur": 3596, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460898490, "dur": 1240, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460899734, "dur": 414, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900156, "dur": 21, "ph": "X", "name": "ProcessMessages 20573", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900179, "dur": 81, "ph": "X", "name": "ReadAsync 20573", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900264, "dur": 3, "ph": "X", "name": "ProcessMessages 1575", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900268, "dur": 51, "ph": "X", "name": "ReadAsync 1575", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900321, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900323, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900355, "dur": 86, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900444, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900488, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900490, "dur": 27, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900518, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900520, "dur": 32, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900554, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900556, "dur": 118, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900677, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900710, "dur": 3, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900714, "dur": 38, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900756, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900758, "dur": 46, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900806, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900808, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900854, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900857, "dur": 25, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900885, "dur": 27, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900916, "dur": 33, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900950, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900952, "dur": 25, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460900980, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901011, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901037, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901065, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901092, "dur": 57, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901151, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901152, "dur": 39, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901193, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901195, "dur": 101, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901303, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901462, "dur": 4, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901468, "dur": 180, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901653, "dur": 6, "ph": "X", "name": "ProcessMessages 2387", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901661, "dur": 177, "ph": "X", "name": "ReadAsync 2387", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901842, "dur": 3, "ph": "X", "name": "ProcessMessages 2973", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460901845, "dur": 162, "ph": "X", "name": "ReadAsync 2973", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902011, "dur": 3, "ph": "X", "name": "ProcessMessages 2707", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902015, "dur": 112, "ph": "X", "name": "ReadAsync 2707", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902131, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902134, "dur": 63, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902200, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902203, "dur": 23, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902230, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902257, "dur": 25, "ph": "X", "name": "ReadAsync 38", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902284, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902285, "dur": 23, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902311, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902339, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460902343, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460905441, "dur": 5, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460905449, "dur": 604, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906060, "dur": 19, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906081, "dur": 55, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906144, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906149, "dur": 94, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906246, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906249, "dur": 91, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906343, "dur": 36, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906381, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906382, "dur": 26, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906412, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906415, "dur": 128, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906550, "dur": 3, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906555, "dur": 224, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906889, "dur": 4, "ph": "X", "name": "ProcessMessages 2492", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460906895, "dur": 116, "ph": "X", "name": "ReadAsync 2492", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907036, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907038, "dur": 392, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907436, "dur": 167, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907606, "dur": 314, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460907929, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460908082, "dur": 3, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460908087, "dur": 1961, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460910053, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460910056, "dur": 1327, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460911389, "dur": 18, "ph": "X", "name": "ProcessMessages 2784", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460911411, "dur": 81, "ph": "X", "name": "ReadAsync 2784", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460911496, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460911500, "dur": 4077, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915586, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915611, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915613, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915649, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915679, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915865, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915901, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915955, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915975, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460915980, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916040, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916094, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916115, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916378, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916411, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916467, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460916487, "dur": 709, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460917199, "dur": 2131, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460919338, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460919341, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460919373, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460919378, "dur": 630, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920014, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920051, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920052, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920078, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920237, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920266, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920395, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920422, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920510, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920535, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920562, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920627, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920647, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920800, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920822, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460920981, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460921004, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460921033, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460921068, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460921093, "dur": 1151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460922250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460922253, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460922273, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460922368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460922370, "dur": 1768, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924145, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924151, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924208, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924212, "dur": 96, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924312, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924342, "dur": 591, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924942, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924980, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460924982, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925010, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925086, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925127, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925129, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925348, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925380, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344460925382, "dur": 2727955, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463653349, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463653354, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463653427, "dur": 4574, "ph": "X", "name": "ProcessMessages 3667", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463658010, "dur": 2664, "ph": "X", "name": "ReadAsync 3667", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463660681, "dur": 24, "ph": "X", "name": "ProcessMessages 1515", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463660708, "dur": 532, "ph": "X", "name": "ReadAsync 1515", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463661244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463661246, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463661292, "dur": 261, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 888, "tid": 12884901888, "ts": 1750344463661556, "dur": 6845, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464336543, "dur": 361, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 888, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 888, "tid": 8589934592, "ts": 1750344460877139, "dur": 80573, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 888, "tid": 8589934592, "ts": 1750344460957714, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 888, "tid": 8589934592, "ts": 1750344460957720, "dur": 1329, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464336906, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 888, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 888, "tid": 4294967296, "ts": 1750344460845144, "dur": 2827276, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344460851713, "dur": 17154, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344463686786, "dur": 381411, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344464068430, "dur": 247834, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344464068681, "dur": 5084, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344464316281, "dur": 9952, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344464322181, "dur": 2559, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 888, "tid": 4294967296, "ts": 1750344464326238, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464336916, "dur": 29, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750344464090985, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464091069, "dur": 45235, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464136311, "dur": 94, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464136472, "dur": 276, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464136892, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344464137001, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_609C16FA6C001C3C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344464137455, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D9B7D3234F369077.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344464141199, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344464136792, "dur": 6042, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464142841, "dur": 162081, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464304925, "dur": 122, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464309380, "dur": 1600, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750344464137678, "dur": 5265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464142957, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464143509, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464143885, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464144593, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464144747, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464144972, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464145170, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464145437, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464145534, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464145602, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464145899, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464145980, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464146170, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464146638, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464147107, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464147869, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464148323, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464148797, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464149262, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464149729, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464150206, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464150996, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464151466, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464151992, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464152510, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464152970, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464153528, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464154254, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464154343, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464154728, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464154972, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464155689, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464155827, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750344464156266, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464156381, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464156536, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464156742, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464157551, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464158363, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464158463, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344464158605, "dur": 144007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344464302613, "dur": 2125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464137707, "dur": 5253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464142969, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464143208, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464143652, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464143899, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464144027, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BD066AF91216C895.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464144580, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464144774, "dur": 8176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750344464152951, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464153071, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464153228, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750344464153777, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464154218, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344464154390, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750344464154845, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464154956, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464155639, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464155776, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464156307, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464156363, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464156512, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464156750, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464157560, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464158375, "dur": 144244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344464302620, "dur": 2099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464137476, "dur": 5402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464144440, "dur": 322, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1750344464142884, "dur": 1879, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464144924, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464145478, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344464145729, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344464145982, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750344464146101, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464146159, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464146706, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464147574, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464148022, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464148454, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464148910, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464149375, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464149869, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464150351, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750344464150757, "dur": 95, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464151895, "dur": 144103, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750344464301489, "dur": 1120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344464302609, "dur": 2074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464137798, "dur": 5183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464142981, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464143210, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464143277, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464143655, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E84E2AFCCEFD9CC7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464143879, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464143969, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464144248, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464144670, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344464144845, "dur": 5326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750344464150173, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464150293, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750344464150691, "dur": 149, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344464151558, "dur": 145688, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750344464302607, "dur": 2063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464137522, "dur": 5365, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464142900, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464143127, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464143527, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4C2401B10833EC61.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464143885, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464143997, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464144205, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464144572, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464144756, "dur": 9760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344464154517, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464154831, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464155065, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344464155506, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464155793, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464156109, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464156192, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344464156800, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464156934, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344464157365, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464157595, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464157727, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344464158194, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464158382, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464158457, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344464158630, "dur": 143984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344464302615, "dur": 2114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464137560, "dur": 5341, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464142909, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344464143887, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464144555, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464144898, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344464145322, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464145384, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750344464145447, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464145508, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750344464145596, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464145788, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750344464145848, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750344464145901, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464145970, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750344464146124, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464146207, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464146937, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464147971, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464148414, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464148843, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464149274, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464149736, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464150220, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464150795, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464151265, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464151877, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464152561, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464153282, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464153978, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464154247, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464154335, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464154729, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464154958, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464155604, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464155696, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344464155828, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750344464156199, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464156376, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464156535, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464156743, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464157543, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464158371, "dur": 144249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344464302621, "dur": 2066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464137611, "dur": 5303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464142926, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344464143217, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344464143398, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344464143868, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464143979, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344464144721, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464144849, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344464144995, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464145109, "dur": 5096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344464150206, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464150368, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344464150865, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344464152466, "dur": 146759, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750344464137651, "dur": 5276, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464142936, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344464143230, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344464143415, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464143882, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464144002, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_E90840FC7BA4DF8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344464144204, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464144556, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464144637, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464144916, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464145116, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464145252, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464145361, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750344464145414, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464145539, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464145734, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344464145911, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464146008, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750344464146138, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464146798, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464147675, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464148387, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464149054, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464149722, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464150587, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464151298, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464151956, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464152678, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464153389, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464154222, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464154328, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464154718, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464154965, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464155612, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464155691, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344464155843, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464155915, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750344464156378, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464156560, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464156751, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464157568, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464158365, "dur": 143126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464301491, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344464302611, "dur": 2306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344464313726, "dur": 936, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1750344463809730, "dur": 240360, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344463810980, "dur": 84379, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344464023807, "dur": 3446, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344464027257, "dur": 22820, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344464028499, "dur": 18413, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344464057178, "dur": 1113, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750344464056694, "dur": 1810, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750344460889152, "dur": 66, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460889245, "dur": 1588, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460890844, "dur": 286, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460891222, "dur": 363, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460893175, "dur": 3341, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460897399, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1A8907DE4FA741CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460897918, "dur": 1593, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_997BF679851B602C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460899619, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460900747, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460900933, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750344460901112, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750344460901288, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750344460901573, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750344460902856, "dur": 2637, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750344460906015, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750344460891614, "dur": 14520, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460906143, "dur": 53424, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460959568, "dur": 101, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344460959683, "dur": 2700737, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344463660518, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344463660612, "dur": 1972, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750344460892139, "dur": 14066, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460906243, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344460906908, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460907015, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344460907136, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460907219, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460907832, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460908407, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344460908575, "dur": 9692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750344460918269, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460918392, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460918600, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344460918792, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750344460919292, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460919483, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750344460919652, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750344460920285, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460920451, "dur": 1371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460921822, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460922563, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460922641, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460923121, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460923542, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750344460924393, "dur": 35178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460892189, "dur": 14118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460906314, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460906885, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460907458, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460907816, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460908394, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460908511, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460908622, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460908942, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460909347, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460909496, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750344460909662, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460909752, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460910859, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460911962, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460912450, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460912918, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460913386, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460913850, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460914472, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460915153, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750344460915586, "dur": 83, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750344460917685, "dur": 2735275, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750344460892197, "dur": 14124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460906331, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460906782, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460906905, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460907813, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460908209, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460908792, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460908950, "dur": 5965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750344460914916, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460915078, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460915495, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460915803, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460916667, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460917330, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460918083, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460919030, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460919510, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460919912, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460920469, "dur": 1265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460921737, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460921801, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460921908, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460922081, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750344460922507, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460922651, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460923071, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460923528, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460924376, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750344460924556, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750344460924712, "dur": 34832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460892268, "dur": 14081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460906359, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460906888, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460907065, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460907130, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460907822, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_BAA7B95D94018A98.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460907969, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460908029, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460908388, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750344460908513, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460908849, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460909363, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460909541, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460909715, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460910481, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460911030, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460911920, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460912365, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460912816, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460913372, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460914029, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460914703, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460915260, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750344460915385, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460915521, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460916156, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460917216, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460917908, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460918633, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460918690, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460919175, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460919508, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460919930, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750344460919986, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460920428, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460920516, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460921838, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460921911, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750344460922065, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750344460922494, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460922652, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460923082, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460923525, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460924349, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750344460924420, "dur": 35148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460892170, "dur": 14127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460906307, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460906829, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460906891, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0399CE90B9A515B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460907127, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460907311, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D65E8F5246B9B178.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460907483, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460907831, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460908183, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460908673, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460909188, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750344460909485, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750344460909601, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750344460909712, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460911056, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460912212, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460912976, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460913644, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460914389, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460915079, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750344460915517, "dur": 204, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750344460918133, "dur": 2734620, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750344460892251, "dur": 14083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344460906344, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344460906861, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344460907402, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344460907796, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344460908488, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750344460908644, "dur": 6260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750344460914905, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344460914989, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750344460915380, "dur": 87, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344463652652, "dur": 54, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750344460916066, "dur": 2736730, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460892309, "dur": 14063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460906380, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460907406, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460907477, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460907826, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460907996, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460908364, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460908547, "dur": 11017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460919565, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460920074, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460920225, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460921511, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460922654, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460922776, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460923115, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460923189, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460923410, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460923507, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460923636, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460923749, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750344460924221, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460924442, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750344460924554, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750344460924710, "dur": 34832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460892341, "dur": 14095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460906437, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DB43258EFB737D47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344460907503, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_AC1145378078C087.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344460907638, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460908043, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460908416, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460909220, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344460909354, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750344460909452, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750344460909566, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750344460909700, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460910376, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460911075, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460912091, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460912774, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460913465, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460913914, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460914470, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460914965, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460915672, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460916151, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460916595, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460917032, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460917455, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460917964, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460918642, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460919493, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460919973, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460920495, "dur": 1234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460921797, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460921913, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750344460922058, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750344460922445, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460922641, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460923097, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460923517, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460924385, "dur": 34095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750344460958482, "dur": 1046, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750344463666386, "dur": 1031, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 888, "tid": 22, "ts": 1750344464337748, "dur": 4378, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 888, "tid": 22, "ts": 1750344464346569, "dur": 67, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 888, "tid": 22, "ts": 1750344464346883, "dur": 28, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 888, "tid": 22, "ts": 1750344464342228, "dur": 4333, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464346685, "dur": 197, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464346945, "dur": 371, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 888, "tid": 22, "ts": 1750344464332451, "dur": 16666, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}