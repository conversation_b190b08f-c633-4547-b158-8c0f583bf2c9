{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 788, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 788, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 788, "tid": 10, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 788, "tid": 10, "ts": 1750342264745167, "dur": 15, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264745195, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 788, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 788, "tid": 1, "ts": 1750342264564874, "dur": 1068, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 788, "tid": 1, "ts": 1750342264565947, "dur": 17475, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 788, "tid": 1, "ts": 1750342264583424, "dur": 18279, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264745202, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 788, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264564837, "dur": 22559, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587397, "dur": 156761, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587408, "dur": 82, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587496, "dur": 295, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587797, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587801, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587840, "dur": 9, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264587851, "dur": 2023, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264589880, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264589885, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590012, "dur": 4, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590018, "dur": 72, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590092, "dur": 2, "ph": "X", "name": "ProcessMessages 1716", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590095, "dur": 41, "ph": "X", "name": "ReadAsync 1716", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590138, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590141, "dur": 42, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590188, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590191, "dur": 46, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590240, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590242, "dur": 50, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590294, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590296, "dur": 65, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590366, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590431, "dur": 2, "ph": "X", "name": "ProcessMessages 1472", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590434, "dur": 79, "ph": "X", "name": "ReadAsync 1472", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590516, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590518, "dur": 84, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590606, "dur": 2, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590608, "dur": 48, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590660, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590662, "dur": 51, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590715, "dur": 2, "ph": "X", "name": "ProcessMessages 1232", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590718, "dur": 56, "ph": "X", "name": "ReadAsync 1232", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590777, "dur": 1, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590779, "dur": 54, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590836, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590838, "dur": 54, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590895, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590898, "dur": 61, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590962, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264590964, "dur": 1430, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592402, "dur": 13, "ph": "X", "name": "ProcessMessages 20567", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592416, "dur": 51, "ph": "X", "name": "ReadAsync 20567", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592472, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592475, "dur": 47, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592525, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592527, "dur": 47, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592576, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592579, "dur": 44, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592625, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592627, "dur": 39, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592668, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592670, "dur": 35, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592709, "dur": 40, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592752, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592754, "dur": 44, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592802, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592803, "dur": 48, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592854, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592856, "dur": 46, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592905, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592907, "dur": 44, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592953, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264592955, "dur": 43, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593001, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593003, "dur": 36, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593041, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593043, "dur": 43, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593089, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593090, "dur": 50, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593143, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593145, "dur": 68, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593217, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593260, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593261, "dur": 50, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593316, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593358, "dur": 2, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593361, "dur": 41, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593405, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593407, "dur": 42, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593452, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593455, "dur": 50, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593508, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593510, "dur": 49, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593561, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264593563, "dur": 4139, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264597711, "dur": 28, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264597741, "dur": 458, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598201, "dur": 17, "ph": "X", "name": "ProcessMessages 19900", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598220, "dur": 41, "ph": "X", "name": "ReadAsync 19900", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598264, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598267, "dur": 40, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598311, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598313, "dur": 377, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598697, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598729, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598731, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598754, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598860, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264598862, "dur": 680, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264599546, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264599548, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264599608, "dur": 7, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264599616, "dur": 307, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264599927, "dur": 764, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600694, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600699, "dur": 66, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600767, "dur": 9, "ph": "X", "name": "ProcessMessages 1440", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600777, "dur": 15, "ph": "X", "name": "ReadAsync 1440", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600795, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600824, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600845, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600865, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600904, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600928, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600956, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600977, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264600998, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601017, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601037, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601056, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601059, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601099, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601113, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601139, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601163, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601184, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601205, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601246, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601264, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264601265, "dur": 5793, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607068, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607073, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607102, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607104, "dur": 651, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607759, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607762, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607799, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264607804, "dur": 1561, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609376, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609380, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609429, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609471, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609475, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609504, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609507, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609568, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609593, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609708, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609732, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609735, "dur": 151, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609892, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609924, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264609926, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264610079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264610081, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264610108, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264610110, "dur": 1274, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611398, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611428, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611430, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611689, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611720, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611722, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611892, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611930, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264611932, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612091, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612158, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612160, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612321, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612354, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612537, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612577, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612867, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612906, "dur": 44, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264612951, "dur": 157, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613113, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613144, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613175, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613177, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613321, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613350, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613388, "dur": 524, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613920, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613923, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613959, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264613991, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614018, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614069, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614092, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614114, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614138, "dur": 24, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614163, "dur": 24, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614190, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614193, "dur": 248, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614447, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614481, "dur": 20, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614503, "dur": 18, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614526, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614722, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614753, "dur": 14, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614768, "dur": 13, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614785, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614807, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264614809, "dur": 811, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615629, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615633, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615691, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615693, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615768, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615770, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615788, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615792, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615860, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615890, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615919, "dur": 33, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615954, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264615957, "dur": 335, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616301, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616338, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616353, "dur": 32, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616389, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616413, "dur": 20, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616447, "dur": 17, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616466, "dur": 26, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616494, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616495, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616519, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616531, "dur": 62, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616597, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616620, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616622, "dur": 237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616867, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616913, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264616916, "dur": 183, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617106, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617148, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617314, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617357, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617378, "dur": 104, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617487, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617511, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617513, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617544, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264617546, "dur": 1201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264618757, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264618761, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264618830, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264618833, "dur": 373, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619214, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619237, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619263, "dur": 95, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619364, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619397, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619400, "dur": 308, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619716, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619754, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619774, "dur": 41, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619819, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619857, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619876, "dur": 33, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619912, "dur": 19, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619933, "dur": 25, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619960, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264619963, "dur": 441, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620412, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620443, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620445, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620727, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620768, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620790, "dur": 69, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620864, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620898, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264620901, "dur": 253, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264621159, "dur": 17, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264621178, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264621216, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264621235, "dur": 840, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622092, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622096, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622146, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622180, "dur": 231, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622418, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622450, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622545, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622579, "dur": 19, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622601, "dur": 95, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622699, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622703, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622741, "dur": 17, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264622760, "dur": 781, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623549, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623553, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623594, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623614, "dur": 96, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623716, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623756, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623759, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264623980, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264624010, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264624012, "dur": 1425, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264625447, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264625452, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264625515, "dur": 29, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264625545, "dur": 108598, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264734154, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264734158, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264734187, "dur": 27, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264734216, "dur": 4227, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264738447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264738449, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264738528, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 788, "tid": 34359738368, "ts": 1750342264738531, "dur": 5618, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264745215, "dur": 1062, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 788, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 788, "tid": 30064771072, "ts": 1750342264564782, "dur": 36918, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 788, "tid": 30064771072, "ts": 1750342264601702, "dur": 45, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264746280, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 788, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 788, "tid": 25769803776, "ts": 1750342264559330, "dur": 184996, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 788, "tid": 25769803776, "ts": 1750342264559436, "dur": 5277, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 788, "tid": 25769803776, "ts": 1750342264744336, "dur": 66, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 788, "tid": 25769803776, "ts": 1750342264744351, "dur": 21, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 788, "tid": 25769803776, "ts": 1750342264744403, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264746290, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750342264588013, "dur": 55, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264588097, "dur": 1702, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264589812, "dur": 285, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264590192, "dur": 314, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264590751, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750342264592434, "dur": 689, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_567619744B52B229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750342264593836, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750342264595235, "dur": 2070, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750342264597333, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750342264598492, "dur": 439, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750342264590534, "dur": 8451, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264598993, "dur": 138973, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264737967, "dur": 116, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264738096, "dur": 64, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264739169, "dur": 1996, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750342264590801, "dur": 8233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264599044, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750342264599735, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264600245, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264601213, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750342264601418, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750342264601559, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264601909, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264601968, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264603336, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264604214, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264604678, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264605120, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264605560, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264606018, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264606518, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264607030, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264607489, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264607949, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264608378, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264608866, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264609632, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264610010, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264610719, "dur": 2924, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750342264613647, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750342264613798, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264613872, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750342264614387, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264614504, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750342264614722, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750342264615104, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264615224, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264615319, "dur": 127, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264617109, "dur": 56, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264615449, "dur": 1755, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750342264617268, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264617575, "dur": 3030, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750342264620656, "dur": 2633, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750342264623292, "dur": 1213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750342264624507, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750342264624694, "dur": 113288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264590796, "dur": 8211, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264599034, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750342264599448, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264599564, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750342264600244, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750342264600629, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264601135, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264601588, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750342264601929, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264602592, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264603478, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264604301, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264604794, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264605191, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264605594, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264606010, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264606453, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264606857, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264607265, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264607700, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264608230, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264608716, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264609233, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264609722, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264609947, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264610090, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264610669, "dur": 4190, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750342264616486, "dur": 85, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264614904, "dur": 1680, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750342264616591, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264616681, "dur": 3254, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750342264619952, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264620082, "dur": 1825, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750342264621910, "dur": 1293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750342264623273, "dur": 2868, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750342264626146, "dur": 111852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264590927, "dur": 8148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264599087, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264599479, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264599567, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264599684, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264600254, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264600644, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264601202, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264601362, "dur": 8588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264609952, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264610268, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264610529, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264610621, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264612664, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264612800, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264612865, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264613042, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264613295, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264613996, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264614170, "dur": 3551, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264617731, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264617837, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264618147, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264618259, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264619339, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264619505, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264621019, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264621114, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750342264622654, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264623522, "dur": 899, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264624504, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750342264624649, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750342264624709, "dur": 113299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750342264590852, "dur": 8196, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750342264599059, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750342264599532, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750342264599784, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750342264599962, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750342264600219, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750342264600499, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750342264601211, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750342264601423, "dur": 6185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750342264607616, "dur": 126, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750342264608704, "dur": 126128, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750342264590891, "dur": 8171, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264599071, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264599353, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264599417, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264599560, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264601206, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264601414, "dur": 8568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750342264609983, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264610160, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264610286, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264610530, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264610624, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750342264611931, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264612080, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.ref.dll_99352414147018AA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264612229, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264612398, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750342264612610, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750342264613742, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264614108, "dur": 1175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750342264615290, "dur": 1001, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264616341, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750342264616479, "dur": 256, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750342264616738, "dur": 3883, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750342264620665, "dur": 2630, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750342264623297, "dur": 114677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264590948, "dur": 8142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264599102, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264599411, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264599563, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264600252, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264600593, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264601225, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264601462, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264601601, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264601751, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750342264601914, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264601972, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264602676, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264603415, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264604327, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264605021, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264605707, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264606401, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264607095, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264610872, "dur": 4299, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750342264615175, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264615352, "dur": 138, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264615500, "dur": 1524, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750342264617030, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264617118, "dur": 53, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264620429, "dur": 135, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750342264617181, "dur": 3403, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750342264620654, "dur": 2795, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750342264623451, "dur": 114555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264590988, "dur": 8115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264599108, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264599549, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264599635, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264600221, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264601195, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264601258, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264601396, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264601730, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264601966, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264603779, "dur": 264, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1750342264602494, "dur": 1550, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264604044, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264604515, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264604953, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264605406, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264605855, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264606347, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264606811, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264607292, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264610861, "dur": 2773, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750342264613639, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264613832, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750342264614040, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750342264614442, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264614530, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750342264614869, "dur": 2362, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750342264617238, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264617321, "dur": 3113, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750342264620441, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264620561, "dur": 87, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264620659, "dur": 2140, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750342264622803, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264623337, "dur": 83, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750342264623422, "dur": 2766, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750342264626191, "dur": 111776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264591008, "dur": 8114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264599123, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DB43258EFB737D47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750342264599529, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750342264599632, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264600246, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_BAA7B95D94018A98.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750342264600625, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264601216, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750342264601415, "dur": 6195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750342264607611, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264607799, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264608485, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264609121, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264609944, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264615300, "dur": 151, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264610674, "dur": 4796, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750342264615531, "dur": 2534, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750342264618072, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264618205, "dur": 3253, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750342264621464, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750342264621583, "dur": 2677, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750342264624264, "dur": 113705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750342264743366, "dur": 911, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 788, "tid": 10, "ts": 1750342264746403, "dur": 2729, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 788, "tid": 10, "ts": 1750342264749303, "dur": 7554, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 788, "tid": 10, "ts": 1750342264745184, "dur": 11719, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}