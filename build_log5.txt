[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0b2-d4c40f50eb81' branch; Version is '6000.2.0b2 (2bdac9ac0d74) revision 2874057'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'pt' Physical Memory: 7875 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-salam" at "2025-06-19T14:19:11.4891072Z"
Date: 2025-06-19T14:19:11Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Unity.exe
-batchmode
-quit
-projectPath
.
-buildTarget
StandaloneWindows64
-logFile
build_log5.txt
Successfully changed project path to: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
C:/Code Projects/Octopath Traveler/TacticalcombatSystem
It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
Fatal Error! It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
Crash!!!
SymInit: Symbol-SearchPath: 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Mono;.;C:\Code Projects\Octopath Traveler\TacticalcombatSystem;C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\BurstCache\JIT;C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor;C:\WINDOWS;C:\WINDOWS\system32;', symOptions: 534, UserName: 'salam'
OS-Version: 10.0.0
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Unity.exe:Unity.exe (00007FF6B2E40000), size: 290816 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 6000.2.0.56009
C:\WINDOWS\SYSTEM32\ntdll.dll:ntdll.dll (00007FFDDB620000), size: 2510848 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNEL32.DLL:KERNEL32.DLL (00007FFDD9F50000), size: 823296 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNELBASE.dll:KERNELBASE.dll (00007FFDD8A30000), size: 4096000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\USER32.dll:USER32.dll (00007FFDDAF70000), size: 1875968 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\win32u.dll:win32u.dll (00007FFDD9390000), size: 159744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\System32\GDI32.dll:GDI32.dll (00007FFDDB5B0000), size: 176128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\gdi32full.dll:gdi32full.dll (00007FFDD8F70000), size: 1273856 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\msvcp_win.dll:msvcp_win.dll (00007FFDD8800000), size: 667648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\ucrtbase.dll:ucrtbase.dll (00007FFDD8E20000), size: 1355776 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\IMM32.DLL:IMM32.DLL (00007FFDD9F10000), size: 196608 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Unity.dll:Unity.dll (00007FFD2BA60000), size: 87244800 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 6000.2.0.56009
C:\WINDOWS\System32\CRYPT32.dll:CRYPT32.dll (00007FFDD88B0000), size: 1536000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3775
C:\WINDOWS\System32\ADVAPI32.dll:ADVAPI32.dll (00007FFDDAEB0000), size: 733184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\msvcrt.dll:msvcrt.dll (00007FFDD96C0000), size: 692224 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 7.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\s3tcompress.dll:s3tcompress.dll (00007FFDC5E50000), size: 180224 (result: 0), SymType: '-deferred-', PDB: ''
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\libfbxsdk.dll:libfbxsdk.dll (00007FFD47B10000), size: 8019968 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2020.3.7.0
C:\WINDOWS\System32\sechost.dll:sechost.dll (00007FFDD9610000), size: 679936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\etccompress.dll:etccompress.dll (00007FFD45B40000), size: 5070848 (result: 0), SymType: '-deferred-', PDB: ''
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\ispc_texcomp.dll:ispc_texcomp.dll (00007FFD50410000), size: 1826816 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\RPCRT4.dll:RPCRT4.dll (00007FFDD93E0000), size: 1134592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\compress_bc7e.dll:compress_bc7e.dll (00007FFD502B0000), size: 1433600 (result: 0), SymType: '-deferred-', PDB: ''
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\umbraoptimizer64.dll:umbraoptimizer64.dll (00007FFD4FDA0000), size: 1187840 (result: 0), SymType: '-deferred-', PDB: ''
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\WinPixEventRuntime.dll:WinPixEventRuntime.dll (00007FFDD2EC0000), size: 53248 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.0.2201.24001
C:\WINDOWS\System32\ole32.dll:ole32.dll (00007FFDD9D60000), size: 1695744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\FreeImage.dll:FreeImage.dll (0000000180000000), size: 5783552 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 3.18.0.0
C:\WINDOWS\System32\combase.dll:combase.dll (00007FFDD99D0000), size: 3690496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WS2_32.dll:WS2_32.dll (00007FFDDB140000), size: 475136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\SYSTEM32\MSVCP140.dll:MSVCP140.dll (00007FFDBD660000), size: 577536 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34433.0
C:\WINDOWS\System32\SETUPAPI.dll:SETUPAPI.dll (00007FFDDA120000), size: 4743168 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SHELL32.dll:SHELL32.dll (00007FFDDA760000), size: 7610368 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\VCRUNTIME140.dll:VCRUNTIME140.dll (00007FFDBD640000), size: 122880 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34433.0
C:\WINDOWS\SYSTEM32\VCRUNTIME140_1.dll:VCRUNTIME140_1.dll (00007FFDBD630000), size: 49152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.42.34433.0
C:\WINDOWS\System32\wintypes.dll:wintypes.dll (00007FFDD9210000), size: 1523712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SHLWAPI.dll:SHLWAPI.dll (00007FFDDB4A0000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\bcrypt.dll:bcrypt.dll (00007FFDD8650000), size: 155648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\SYSTEM32\d3d11.dll:d3d11.dll (00007FFDD4590000), size: 2514944 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll:COMCTL32.dll (00007FFDC3710000), size: 2727936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 6.10.26100.4202
C:\WINDOWS\System32\OLEAUT32.dll:OLEAUT32.dll (00007FFDD98E0000), size: 921600 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\d3d12.dll:d3d12.dll (00007FFDD3290000), size: 143360 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3624
C:\WINDOWS\SYSTEM32\dwmapi.dll:dwmapi.dll (00007FFDD57F0000), size: 221184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WINTRUST.dll:WINTRUST.dll (00007FFDD8770000), size: 540672 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4351
C:\WINDOWS\SYSTEM32\dxgi.dll:dxgi.dll (00007FFDD5660000), size: 1265664 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL:IPHLPAPI.DLL (00007FFDD6F60000), size: 208896 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\HID.DLL:HID.DLL (00007FFDD6C00000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\OPENGL32.dll:OPENGL32.dll (00007FFDA2970000), size: 1130496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\WINHTTP.dll:WINHTTP.dll (00007FFDD25A0000), size: 1171456 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 15260, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
C:\WINDOWS\SYSTEM32\WINMM.dll:WINMM.dll (00007FFDCFEF0000), size: 217088 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\GLU32.dll:GLU32.dll (00007FFDC5E20000), size: 184320 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\SketchUpAPI.dll:SketchUpAPI.dll (00007FFD44CD0000), size: 8990720 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: ********
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\NativeProxyHelper.dll:NativeProxyHelper.dll (00007FFD4FBE0000), size: 1789952 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\SYSTEM32\VERSION.dll:VERSION.dll (00007FFDC5040000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              27dfc30a0f2a496c8e1039ed13c005cd
  Correlation Id:          397a8fcc2086f78a32fc44fc75a51103
  External correlation Id: 7987555647062280714
  Machine Id:              GjMHUjoGIR39xUENxO/5NC6b5IU=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-salam" (connect: 0.00s, validation: 0.00s, handshake: 0.06s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-salam-notifications" at "2025-06-19T14:19:11.5487823Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
C:\WINDOWS\SYSTEM32\WSOCK32.dll:WSOCK32.dll (00007FFDCB210000), size: 40960 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\SketchUpCommonPreferences.dll:SketchUpCommonPreferences.dll (00007FFDC4020000), size: 499712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: ********
C:\WINDOWS\SYSTEM32\Secur32.dll:Secur32.dll (00007FFDD5270000), size: 53248 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\dxcore.dll:dxcore.dll (00007FFDD5610000), size: 315392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\SSPICLI.DLL:SSPICLI.DLL (00007FFDD77D0000), size: 299008 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\OpenRL.dll:OpenRL.dll (0000020B2E600000), size: 12779520 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.5.0.2907
C:\WINDOWS\SYSTEM32\MSVCP100.dll:MSVCP100.dll (000000005CE00000), size: 622592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
C:\WINDOWS\SYSTEM32\MSVCR100.dll:MSVCR100.dll (000000005CD20000), size: 860160 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\embree.dll:embree.dll (00007FFD3BBB0000), size: 16711680 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.14.0.0
C:\WINDOWS\SYSTEM32\cfgmgr32.DLL:cfgmgr32.DLL (00007FFDD82B0000), size: 356352 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 6872291690911-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\tbb.dll:tbb.dll (00007FFDC41E0000), size: 303104 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2020.3.2022.1111
C:\WINDOWS\SYSTEM32\MSWSOCK.DLL:MSWSOCK.DLL (00007FFDD7AA0000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\OpenRL_pthread.dll:OpenRL_pthread.dll (0000020B2E580000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.9.0.0
C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll:directxdatabasehelper.dll (00007FFDD55A0000), size: 397312 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\kernel.appcore.dll:kernel.appcore.dll (00007FFDD7530000), size: 110592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\bcryptPrimitives.dll:bcryptPrimitives.dll (00007FFDD90B0000), size: 626688 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\MSASN1.dll:MSASN1.dll (00007FFDD7E80000), size: 77824 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
[Licensing::Client] Successfully updated license, isAsync: True, time: 0.00
C:\WINDOWS\system32\uxtheme.dll:uxtheme.dll (00007FFDD5450000), size: 716800 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\windows.storage.dll:windows.storage.dll (00007FFDD63A0000), size: 8749056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\System32\SHCORE.dll:SHCORE.dll (00007FFDDB1C0000), size: 987136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\profapi.dll:profapi.dll (00007FFDD8680000), size: 192512 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\CRYPTSP.dll:CRYPTSP.dll (00007FFDD7D50000), size: 110592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\system32\IconCodecService.dll:IconCodecService.dll (00007FFDADAC0000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\WindowsCodecs.dll:WindowsCodecs.dll (00007FFDD3A60000), size: 2330624 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
[Licensing::Client] Successfully resolved entitlement details
C:\WINDOWS\system32\rsaenh.dll:rsaenh.dll (00007FFDD7490000), size: 237568 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
[Licensing::Module] Licensing Background thread has ended after 0.07s
C:\WINDOWS\SYSTEM32\CRYPTBASE.dll:CRYPTBASE.dll (00007FFDD7D70000), size: 49152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\System32\clbcatq.dll:clbcatq.dll (00007FFDD9500000), size: 688128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2001.12.10941.16384
C:\WINDOWS\System32\imagehlp.dll:imagehlp.dll (00007FFDDA5B0000), size: 131072 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\netprofm.dll:netprofm.dll (00007FFDD1F10000), size: 413696 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.2454
C:\WINDOWS\SYSTEM32\dbghelp.dll:dbghelp.dll (00007FFDD5D70000), size: 2363392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\npmproxy.dll:npmproxy.dll (00007FFDC95E0000), size: 102400 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150

========== OUTPUTTING STACK TRACE ==================

0x00007FFDD8AF85EA (KERNELBASE) RaiseException
0x00007FFD2D629C29 (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FFD2D62A910 (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FFD2E2D754D (Unity) DebugStringToFilePostprocessedStacktrace
0x00007FFD2E2D68C9 (Unity) DebugStringToFile
0x00007FFD2D7251E5 (Unity) HandleProjectAlreadyOpenInAnotherInstance
0x00007FFD2D72703D (Unity) Application::InitializeProject
0x00007FFD2DD0CBB5 (Unity) UnityMain
0x00007FF6B2E42F2A (Unity) __scrt_common_main_seh
0x00007FFDD9F7E8D7 (KERNEL32) BaseThreadInitThunk
0x00007FFDDB65C34C (ntdll) RtlUserThreadStart

========== END OF STACKTRACE ===========

A crash has been intercepted by the crash handler. For call stack and other details, see the latest crash report generated in:
 * C:/Users/<USER>/AppData/Local/Temp/Unity/Editor/Crashes
