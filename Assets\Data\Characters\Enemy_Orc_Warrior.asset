%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7f12adf60c39ab34ea3978fe24abd1e6, type: 3}
  m_Name: Enemy_Orc_Warrior
  m_EditorClassIdentifier: Assembly-CSharp::OctopathBattle.Core.CharacterData
  CharacterName: 
  Description: Orc
  CharacterPortrait: {fileID: 0}
  BattleSprite: {fileID: 0}
  BaseHP: 100
  BaseMP: 50
  BaseAttack: 20
  BaseDefense: 15
  BaseMagicAttack: 18
  BaseMagicDefense: 12
  BaseSpeed: 10
  BaseAccuracy: 95
  BaseEvasion: 5
  BaseCritical: 5
  PrimaryJob: {fileID: 0}
  SecondaryJob: {fileID: 0}
  BreakShield: 0
  CanBeBreak: 1
  WeaponWeaknesses: 
  ElementalWeaknesses: 
  StartingWeapon:
    WeaponName: 
    WeaponType: 0
    AttackPower: 0
    MagicPower: 0
    StatModifiers: []
    GrantedAbilities: []
  StartingItems: []
  StartingLevel: 1
  ExperiencePoints: 0
  ExperienceGrowthCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
