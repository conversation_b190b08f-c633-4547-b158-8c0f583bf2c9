%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9a8c4b2f1e3d5c7a8b9f0e1d2c3a4b5c, type: 3}
  m_Name: Enemy_Goblin_Scout
  m_EditorClassIdentifier: Assembly-CSharp::OctopathBattle.Core.CharacterData
  CharacterName: Goblin Archer
  Description: A quick and nimble goblin with deadly accuracy and poisoned arrows.
  CharacterPortrait: {fileID: 0}
  CharacterLevel: 5
  BaseHP: 85
  BaseMP: 35
  BaseAttack: 28
  BaseDefense: 12
  BaseMagicAttack: 18
  BaseMagicDefense: 16
  BaseSpeed: 26
  BaseAccuracy: 92
  BaseEvasion: 20
  BaseCritical: 15
  PrimaryJob: {fileID: 0}
  SecondaryJob: {fileID: 0}
  BreakShield: 2
  WeaponWeaknesses:
  - 0
  - 2
  ElementalWeaknesses:
  - 1
  - 3
  StartingWeapon:
    WeaponName: Poison Bow
    WeaponType: 6
    AttackPower: 20
    MagicPower: 8
    StatModifiers:
    - Stat: 7
      Value: 10
      Type: 0
    - Stat: 9
      Value: 5
      Type: 0
    GrantedAbilities: []
  StartingItems: []
