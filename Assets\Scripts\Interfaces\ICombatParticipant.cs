using System.Collections.Generic;
using UnityEngine;

namespace TacticalCombatSystem.Interfaces
{
    /// <summary>
    /// Interface for any object that can participate in combat
    /// </summary>
    public interface ICombatParticipant
    {
        // Basic stats
        string ParticipantName { get; }
        int CurrentHP { get; set; }
        int MaxHP { get; }
        int CurrentMP { get; set; }
        int MaxMP { get; }
        int Attack { get; }
        int Defense { get; }
        int MagicAttack { get; }
        int MagicDefense { get; }
        int Speed { get; }
        float CriticalChance { get; }
        float CriticalMultiplier { get; }
        bool IsAlive { get; }
        bool IsPlayerControlled { get; }
        
        // Combat actions
        List<TacticalCombatSystem.Interfaces.ICombatAction> GetAvailableActions();
        bool CanPerformAction(TacticalCombatSystem.Interfaces.ICombatAction action);
        
        // Status effects
        void ApplyStatusEffect(object effect);
        void RemoveStatusEffect(object effect);
        bool HasStatusEffect(string effectId);
        List<object> GetStatusEffects();
        
        // Targeting
        bool CanTarget(TacticalCombatSystem.Interfaces.ICombatParticipant target);
        List<TacticalCombatSystem.Interfaces.ICombatParticipant> GetValidTargets(TacticalCombatSystem.Interfaces.ICombatAction action);
        
        // Combat events
        void OnTurnStart();
        void OnTurnEnd();
        void OnActionPerformed(TacticalCombatSystem.Interfaces.ICombatAction action);
        void OnDamageTaken(int amount, TacticalCombatSystem.Interfaces.ICombatParticipant source);
        void OnHealed(int amount, TacticalCombatSystem.Interfaces.ICombatParticipant source);
        void OnStatusEffectApplied(object effect);
        void OnStatusEffectRemoved(object effect);
    }
}
