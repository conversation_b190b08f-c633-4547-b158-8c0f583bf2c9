%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbeec0c02180f164d9306d7eb28dedd0, type: 3}
  m_Name: Player_Mage_Data
  m_EditorClassIdentifier: 
  characterName: Cyrus
  description: A scholar seeking forbidden knowledge
  portrait: {fileID: 0}
  battleSprite: {fileID: 0}
  level: 5
  characterType: 0
  isPlayable: 1
  baseStats:
    MaxHP: 85
    MaxMP: 65
    Attack: 18
    Defense: 15
    MagicAttack: 42
    MagicDefense: 35
    Speed: 25
    Luck: 20
    CurrentHP: 85
    CurrentMP: 65
  primaryJob: {fileID: 0}
  secondaryJob: {fileID: 0}
  unlockedJobs: []
  aiPersonality: 0
  availableActions: []
  weaponWeaknesses: []
  elementalWeaknesses: []
  elementalResistances:
  - 1
  - 2
  elementalImmunities: []
  baseShieldCount: 0
  canBeBreak: 0
  breakRecoveryTime: 0
  startingAbilities: []
  learnableAbilities: []
  defaultWeapon: {fileID: 0}
  usableWeaponTypes:
  - 3
  - 4
  voiceClip: {fileID: 0}
  deathSound: {fileID: 0}
  victorySound: {fileID: 0}
