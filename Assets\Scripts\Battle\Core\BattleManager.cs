using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Battle
{
    public class BattleManager : MonoBehaviour, IBattleManager
    {
        public bool IsPlayerTurn { get; private set; }
        
        // Explicit interface implementation
        IBattleManager IBattleManager.Instance => this;
        
        // Events
        public event Action OnBattleStarted;
        public event Action<ICombatParticipant> OnTurnStarted;
        public event Action<ICombatParticipant, int> OnCharacterDamaged;
        public event Action<ICombatParticipant, int> OnCharacterHealed;
        public event Action<ICombatParticipant, object> OnStatusEffectApplied;
        public event Action<bool> OnBattleEnded;
        public event Action<ICombatParticipant> OnTurnEnded;
        
        // Events
        public delegate void CharacterAction(ICombatParticipant character);
        public event CharacterAction CharacterSelected;
        public event CharacterAction CharacterDeselected;
        public event CharacterAction CharacterTurnStarted;
        public event CharacterAction CharacterTurnEnded;
        public event System.Action<ICombatParticipant> OnCharacterTurnStart;
        public event System.Action<ICombatParticipant> OnCharacterTurnEnd;
        
        // Explicit interface implementations
        event System.Action<ICombatParticipant> IBattleManager.CharacterSelected { add => CharacterSelected += value; remove => CharacterSelected -= value; }
        event System.Action<ICombatParticipant> IBattleManager.CharacterDeselected { add => CharacterDeselected += value; remove => CharacterDeselected -= value; }
        event System.Action<ICombatParticipant> IBattleManager.CharacterTurnStarted { add => CharacterTurnStarted += value; remove => CharacterTurnStarted -= value; }
        event System.Action<ICombatParticipant> IBattleManager.CharacterTurnEnded { add => CharacterTurnEnded += value; remove => CharacterTurnEnded -= value; }
        
        // Current battle state
        private ICombatParticipant currentTurnCharacter;
        private List<ICombatParticipant> turnOrder = new List<ICombatParticipant>();
        private int currentTurnIndex = 0;
        
        [Header("Battle State")]
        [SerializeField] private List<ICombatParticipant> playerTeam = new List<ICombatParticipant>();
        [SerializeField] private List<ICombatParticipant> enemyTeam = new List<ICombatParticipant>();
        
        // Singleton pattern
        public static IBattleManager Instance { get; private set; }
        
        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        
        private void Start()
        {
            InitializeBattle();
            OnBattleStarted?.Invoke();
        }
        
        // Battle management methods
        public void StartBattle()
        {
            InitializeBattle();
        }

        public void EndBattle(bool playerWon)
        {
            // Stop any ongoing turns
            StopAllCoroutines();

            // Handle battle end logic
            Debug.Log(playerWon ? "Player won!" : "Player lost!");

            // Disable input
            IsPlayerTurn = false;
        }

        public void StartTurn(ICombatParticipant character)
        {
            StartTurnInternal(character);
        }

        public void EndTurn()
        {
            if (currentTurnCharacter != null)
            {
                currentTurnCharacter.OnTurnEnd();
                OnTurnEnded?.Invoke(currentTurnCharacter);

                // Move to next character
                NextTurn();
            }
        }

        private void StartTurnInternal(ICombatParticipant character)
        {
            if (character == null) return;
            
            currentTurnCharacter = character;
            IsPlayerTurn = playerTeam.Contains(character);
            
            // Notify the character their turn is starting
            character.OnTurnStart();
            
            // Raise events
            CharacterTurnStarted?.Invoke(character);
            OnTurnStarted?.Invoke(character);
            
            // If it's an enemy's turn, let the AI take control
            if (!IsPlayerTurn && character is MonoBehaviour)
            {
                AITurn(character);
            }
        }

        private IEnumerator EndTurnAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            EndTurn();
        }

        public void EndTurn()
        {
            if (currentTurnCharacter != null)
            {
                // Notify the character their turn is ending
                currentTurnCharacter.OnTurnEnd();
                
                // Raise events
                CharacterTurnEnded?.Invoke(currentTurnCharacter);
                OnTurnEnded?.Invoke(currentTurnCharacter);
            }
            
            // Move to next character in turn order
            currentTurnIndex = (currentTurnIndex + 1) % turnOrder.Count;
            
            // If we've gone through all characters, update turn order
            if (currentTurnIndex == 0)
            {
                UpdateTurnOrder();
            }
            
            // Start the next turn
            StartTurn(turnOrder[currentTurnIndex]);
        }
        
        private void InitializeBattle()
        {
            // Set up turn order
            UpdateTurnOrder();
            
            // Start the first turn
            if (turnOrder.Count > 0)
            {
                currentTurnIndex = 0;
                StartTurnInternal(turnOrder[currentTurnIndex]);
            }
        }
        
        private void UpdateTurnOrder()
        {
            // Combine both teams and sort by speed
            turnOrder.Clear();
            turnOrder.AddRange(playerTeam);
            turnOrder.AddRange(enemyTeam);
            turnOrder.Sort((a, b) => b.Speed.CompareTo(a.Speed));
        }

        private void NextTurn()
        {
            currentTurnIndex = (currentTurnIndex + 1) % turnOrder.Count;
            if (turnOrder.Count > 0)
            {
                StartTurnInternal(turnOrder[currentTurnIndex]);
            }
        }

        // Explicit interface implementations for team management
        void IBattleManager.AddToPlayerTeam(ICombatParticipant character) 
        { 
            if (!playerTeam.Contains(character))
                playerTeam.Add(character); 
        }
        
        void IBattleManager.AddToEnemyTeam(ICombatParticipant character) 
        { 
            if (!enemyTeam.Contains(character))
                enemyTeam.Add(character); 
        }
        
        void IBattleManager.RemoveFromBattle(ICombatParticipant character)
        {
            playerTeam.Remove(character);
            enemyTeam.Remove(character);
            turnOrder.Remove(character);
            
            // If no characters left on a team, end the battle
            if (playerTeam.Count == 0) EndBattle(false);
            if (enemyTeam.Count == 0) EndBattle(true);
        }
        
        List<ICombatParticipant> IBattleManager.GetPlayerTeam() => new List<ICombatParticipant>(playerTeam);
        List<ICombatParticipant> IBattleManager.GetEnemyTeam() => new List<ICombatParticipant>(enemyTeam);
        List<ICombatParticipant> IBattleManager.GetAllParticipants()
        {
            var allParticipants = new List<ICombatParticipant>();
            allParticipants.AddRange(playerTeam);
            allParticipants.AddRange(enemyTeam);
            return allParticipants;
        }
        
        public void OnCharacterSelected(ICombatParticipant character)
        {
            // Validate that the selected character can act
            if (IsPlayerTurn && character.IsPlayerControlled && character.IsAlive)
            {
                CharacterSelected?.Invoke(character);
            }
        }
        
        public void OnCharacterDeselected(ICombatParticipant character = null)
        {
            CharacterDeselected?.Invoke(character);
        }
        
        public void UseAbility(ICombatParticipant user, ICombatParticipant target, ICombatAction action)
        {
            if (user == null || target == null || action == null) return;
            
            // Validate that the user can use this ability
            if (!IsValidAbilityUse(user, target, action)) return;
            
            // Execute the action
            action.Execute(user, target);
            
            // Log the action
            Debug.Log($"{user.ParticipantName} uses {action.ActionName} on {target.ParticipantName}");
            
            // End the turn if this was a player action
            if (user.IsPlayerControlled)
            {
                EndTurn();
            }
        }
        
        private bool IsValidAbilityUse(ICombatParticipant user, ICombatParticipant target, ICombatAction action)
        {
            // Check if it's the user's turn
            if (user != currentTurnCharacter) return false;
            
            // Check if the user can act
            if (!user.IsAlive) return false;
            
            // Check if the user can perform this action
            if (!user.CanPerformAction(action)) return false;
            
            // Check if the target is valid for this action
            if (!action.IsValidTarget(user, target)) return false;
            
            return true;
        }
        
        private void AITurn(ICombatParticipant aiCharacter)
        {
            // Simple AI: Use a random ability on a random valid target
            var availableActions = aiCharacter.GetAvailableActions();
            if (availableActions.Count == 0) 
            {
                EndTurn();
                return;
            }
            
            var action = availableActions[Random.Range(0, availableActions.Count)];
            var validTargets = aiCharacter.GetValidTargets(action);
            
            if (validTargets.Count > 0)
            {
                var target = validTargets[Random.Range(0, validTargets.Count)];
                // Use a small delay to make the AI feel more natural
                StartCoroutine(ExecuteAIActionAfterDelay(aiCharacter, target, action, 1f));
            }
            else
            {
                // If no valid targets, end turn after a delay
                Debug.Log($"{aiCharacter.ParticipantName} has no valid targets for any ability.");
                Invoke(nameof(EndTurn), 1f);
            }
        }
        
        private IEnumerator ExecuteAIActionAfterDelay(ICombatParticipant user, ICombatParticipant target, ICombatAction action, float delay)
        {
            yield return new WaitForSeconds(delay);
            UseAbility(user, target, action);
        }
        
        public void PlayerAttack(ICombatParticipant attacker, ICombatParticipant target, ICombatAction action)
        {
            if (attacker == null || target == null || !attacker.IsAlive || !target.IsAlive)
                return;
                
            // Use the selected action on the target
            UseAbility(attacker, target, action);
            
            // If this was a damaging action, raise the appropriate event
            if (action is IDamageDealer)
            {
                // Calculate damage here based on attacker's attack and target's defense
                int damage = Mathf.Max(1, attacker.Attack - target.Defense / 2);
                target.OnDamageTaken(damage, attacker);
                OnCharacterDamaged?.Invoke(target, damage);
            }
        }
        

    }
}
