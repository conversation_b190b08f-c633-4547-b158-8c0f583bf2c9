using System;
using System.Collections.Generic;
using UnityEngine;

namespace TacticalCombatSystem.Interfaces.Core
{
    /// <summary>
    /// Interface for managing battle flow and state
    /// </summary>
    public interface IBattleManager
    {
        // Properties
        bool IsPlayerTurn { get; }
        IBattleManager Instance { get; }
        
        // Events
        event Action<ICombatParticipant> CharacterSelected;
        event Action<ICombatParticipant> CharacterDeselected;
        event Action<ICombatParticipant> CharacterTurnStarted;
        event Action<ICombatParticipant> CharacterTurnEnded;
        event Action OnBattleStarted;
        event Action<ICombatParticipant> OnTurnStarted;
        event Action<ICombatParticipant, int> OnCharacterDamaged;
        event Action<ICombatParticipant, int> OnCharacterHealed;
        event Action<ICombatParticipant, object> OnStatusEffectApplied;
        event Action<bool> OnBattleEnded;
        event Action<ICombatParticipant> OnTurnEnded;
        
        // Battle Management
        void StartBattle();
        void EndBattle(bool playerWon);
        void StartTurn(ICombatParticipant character);
        void EndTurn();
        
        // Team Management
        void AddToPlayerTeam(ICombatParticipant character);
        void AddToEnemyTeam(ICombatParticipant character);
        void RemoveFromBattle(ICombatParticipant character);
        
        // Targeting
        List<ICombatParticipant> GetPlayerTeam();
        List<ICombatParticipant> GetEnemyTeam();
        List<ICombatParticipant> GetAllParticipants();
        
        // Actions
        void PlayerAttack(ICombatParticipant attacker, ICombatParticipant target, ICombatAction action);
    }
}
