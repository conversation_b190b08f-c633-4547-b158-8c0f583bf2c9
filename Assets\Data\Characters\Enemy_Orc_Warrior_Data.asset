%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbeec0c02180f164d9306d7eb28dedd0, type: 3}
  m_Name: Enemy_Orc_Warrior_Data
  m_EditorClassIdentifier: 
  characterName: Orc Warrior
  description: A fierce orc warrior with brutal strength
  portrait: {fileID: 0}
  battleSprite: {fileID: 0}
  level: 4
  characterType: 1
  isPlayable: 0
  baseStats:
    MaxHP: 150
    MaxMP: 20
    Attack: 30
    Defense: 25
    MagicAttack: 8
    MagicDefense: 12
    Speed: 18
    Luck: 5
    CurrentHP: 150
    CurrentMP: 20
  primaryJob: {fileID: 0}
  secondaryJob: {fileID: 0}
  unlockedJobs: []
  aiPersonality: 0
  availableActions: []
  weaponWeaknesses:
  - 0
  - 3
  elementalWeaknesses:
  - 1
  - 3
  elementalResistances:
  - 0
  elementalImmunities: []
  baseShieldCount: 4
  canBeBreak: 1
  breakRecoveryTime: 2
  startingAbilities: []
  learnableAbilities: []
  defaultWeapon: {fileID: 0}
  usableWeaponTypes:
  - 0
  - 1
  voiceClip: {fileID: 0}
  deathSound: {fileID: 0}
  victorySound: {fileID: 0}
