[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0b2-d4c40f50eb81' branch; Version is '6000.2.0b2 (2bdac9ac0d74) revision 2874057'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-salam" at "2025-06-19T14:08:46.8782393Z"
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'pt' Physical Memory: 7875 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-19T14:08:46Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Unity.exe
-batchmode
-quit
-projectPath
.
-buildTarget
StandaloneWindows64
-logFile
build_log3.txt
Successfully changed project path to: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
C:/Code Projects/Octopath Traveler/TacticalcombatSystem
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12472]  Target information:

Player connection [12472]  * "[IP] *************** [Port] 55504 [Flags] 2 [Guid] 3550987521 [EditorId] 3550987521 [Version] 1048832 [Id] WindowsEditor(7,Andy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12472]  * "[IP] ************* [Port] 55504 [Flags] 2 [Guid] 3550987521 [EditorId] 3550987521 [Version] 1048832 [Id] WindowsEditor(7,Andy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12472] Host joined multi-casting on [***********:54997]...
Player connection [12472] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 15260, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              4ccedd4dafda4938b3a8c2b89888d149
  Correlation Id:          397a8fcc2086f78a32fc44fc75a51103
  External correlation Id: 2948199196080519328
  Machine Id:              GjMHUjoGIR39xUENxO/5NC6b5IU=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-salam" (connect: 0.00s, validation: 0.00s, handshake: 0.05s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-salam-notifications" at "2025-06-19T14:08:46.9340188Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 6872291690911-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
[Licensing::Client] Successfully updated license, isAsync: True, time: 0.00
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Licensing Background thread has ended after 0.07s
[Package Manager] Connected to IPC stream "Upm-18328" after 0.2 seconds.
Library Redirect Path: Library/
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 42 packages:
  Packages from [https://packages.unity.com]:
    com.unity.cinemachine@3.1.4 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.cinemachine@b66fdb7cd1f2)
    com.unity.ide.visualstudio@2.0.23 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13)
    com.unity.splines@2.8.1 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.splines@b909627b5095)
    com.unity.mathematics@1.3.2 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.mathematics@8017b507cc74)
    com.unity.settings-manager@2.1.0 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.settings-manager@41738c275190)
  Built-in packages:
    com.unity.multiplayer.center@1.0.0 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546)
    com.unity.modules.accessibility@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director)
    com.unity.modules.imageconversion@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture)
    com.unity.modules.terrain@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr)
    com.unity.modules.subsystems@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems)
    com.unity.modules.hierarchycore@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore)
    com.unity.test-framework@1.5.1 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.test-framework@b10ae3e6f84c)
    com.unity.ext.nunit@2.0.5 (location: C:\Code Projects\Octopath Traveler\TacticalcombatSystem\Library\PackageCache\com.unity.ext.nunit@031a54704bff)
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.57 seconds
Targeting platform: StandaloneWindows64
Refreshing native plugins compatible for Editor in 0.21 ms, found 0 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b2 (2bdac9ac0d74)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Code Projects/Octopath Traveler/TacticalcombatSystem/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) UHD Graphics (ID=0xa7a9)
    Vendor:   Intel
    VRAM:     3937 MB
    Driver:   31.0.101.4502
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56328
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.002980 seconds.
- Loaded All Assemblies, in  0.328 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.294 seconds
Domain Reload Profiling: 622ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (132ms)
		LoadAssemblies (107ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (129ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (116ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (295ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (261ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (47ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (105ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
A meta data file (.meta) exists but its asset 'Assets/Scripts/Core/ICharacter.cs' can't be found. When moving or deleting files outside of Unity, please ensure that the corresponding .meta file is moved or deleted along with it.
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[ScriptCompilation] Requested script compilation because: Assembly Definition File(s) changed
[ScriptCompilation] Requested script compilation because: Assetdatabase observed changes in script compilation related files
Starting: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\bee_backend.exe --ipc --defer-dag-verification --dagfile="Library/Bee/1900b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
DisplayProgressbar: Compiling Scripts
ExitCode: 4 Duration: 0s183ms
Rebuilding DAG because FileSignature timestamp changed: Library/Bee/1900b0aE-inputdata.json
*** Tundra requires additional run (0.15 seconds), 1 items updated, 281 evaluated
Starting: C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Tools\netcorerun\netcorerun.exe "C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Data\Tools\BuildPipeline\ScriptCompilationBuildProgram.exe" "Library/Bee/1900b0aE.dag.json" "Library/Bee/1900b0aE-inputdata.json" "Library\Bee\buildprogram0.traceevents"
WorkingDir: C:/Code Projects/Octopath Traveler/TacticalcombatSystem
ExitCode: -********* Duration: 6s272ms
One or more cyclic dependencies detected between assemblies: Assembly-CSharp-Editor, Assembly-CSharp, Assets/Scripts/Battle/TacticalCombatSystem.Battle.asmdef, Assets/Scripts/Core/TacticalCombatSystem.Core.asmdef
   at ScriptCompilationBuildProgram.ScriptCompilation.OrderByDependencies(AssemblyData[] assemblies)
   at ScriptCompilationBuildProgram.ScriptCompilation.SetupScriptCompilation(AssemblyData[] inputAssemblies2, String dagName, ScriptCompilationData scd)
   at ScriptCompilationBuildProgram.ScriptCompilationBuildProgram.Main(String[] args)
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at Program.Main(String[] args)
AssetDatabase: script compilation time: 6.836964s
Scripts have compiler errors.
Exiting without the bug reporter. Application will terminate with return code 1